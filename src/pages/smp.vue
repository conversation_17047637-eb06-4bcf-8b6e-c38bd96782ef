<template>
  <q-page class="q-pa-lg">
    <CustomTable
      :data="smp"
      :columns="columns"
      :tabletitle="$t('smp')"
      :isDeleteAvailable="false"
      :isAddAvailable="false"
      :isEditAvailable="false"
      :pagination.sync="pagination"
      @onRequest="onRequest"
      :loading="loading"
      :filter.sync="filter"
    >
      <template v-slot:searchfield="{ props }">
        <q-input
          outlined
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      <span class="q-ml-lg col-1 q-mr-sm"> {{ $t("period") }}: </span>
          <q-input
            outlined
            dense
            type="date"
            v-model="startDate"
          />
          <q-input
            outlined
            dense
            type="date"
            v-model="endDate"
          />
        <q-btn
          flat
          round
          dense
          icon="download"
          :disable="!startDate || !endDate"
          color="black"
          @click="exportToExcel"
        />
      </template>
      <template v-slot:full_name="{ props }">
        {{ props.row.full_name }}
      </template>
      <template v-slot:oper_date_time="{ props }">
        {{ formattedDate(props.row.oper_date_time) }}
      </template>
      <template v-slot:product_id="{ props }">
        {{ props.row.product_id }}
      </template>
      <template v-slot:amount_kzt="{ props }">
        {{ props.row.amount_kzt }}
      </template>
      <template v-slot:iin="{ props }">
        {{ props.row.iin }}
      </template>
      <template v-slot:person_type="{ props }">
        {{ props.row.person_type }}
      </template>
      <template v-slot:mobile_number="{ props }">
        {{ props.row.mobile_number }}
      </template>
      <template v-slot:lic_account="{ props }">
        {{ props.row.lic_account }}
      </template>
      <template v-slot:bank_name="{ props }">
        {{ props.row.bank_name }}
      </template>
    </CustomTable>
    <router-view></router-view>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";
import datemixin from "../mixins/datemixin";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable")
  },
  data() {
    return {
      mode: "list",
      selectedValue: "",
      selectedFilter: "",

      pagination: {
        sortBy: "oper_date_time",
        descending: true,
        page: 1,
        rowsPerPage: 10,
        rowsNumber: 200
      },
      loading: false,
      filter: "",
      startDate: "",
      endDate: ""
    };
  },

  created() {},

  watch: {
    selectedFilter: function(newval, oldval) {
      if (newval !== oldval) {
        this.selectedValue = "";
      }
      if (!newval) {
        const filter = this.filter;
        const pagination = this.pagination;
        this.onRequest({ filter, pagination });
      }
    },
    startDate(newVal) {
    this.onRequest({ pagination: this.pagination, filter: this.filter });
  },
  endDate(newVal) {
    this.onRequest({ pagination: this.pagination, filter: this.filter });
  }
  },

  computed: mapState({
    smp: state => state.events.smp,
    columns() {
      return [
        { name: "full_name", label: this.$t('full_name'), field: "full_name", sortable: true },
        {
          name: "oper_date_time",
          align: "left",
          label: "Дата",
          field: "oper_date_time",
          sortable: true
        },
        {
          name: "product_id",
          label: this.$t('product_id'),
          align: "left",
          field: "product_id",
          sortable: true
        },
        {
          name: "amount_kzt",
          align: "left",
          label: this.$t('amount_kzt'),
          field: "amount_kzt",
          sortable: true
        },
        {
          name: "iin",
          align: "left",
          label: this.$t('iin'),
          field: "iin",
          sortable: true
        },
        {
          name: "person_type",
          label: "Тип отправителя",
          align: "left",
          field: "person_type",
          sortable: true
        },
        {
          name: "mobile_number",
          align: "left",
          label: "Мобильный отправителя",
          field: "mobile_number",
          sortable: true
        },
        {
          name: "lic_account",
          align: "left",
          label: "Номер получателя",
          field: "lic_account",
          sortable: true
        },
        {
          name: "bank_name",
          align: "left",
          label: "Банк получателя",
          field: "bank_name",
          sortable: true
        },
        {
          name: "actions",
          align: "left",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    }
  }),

  methods: {
    ...mapActions({
      getAllSmp: "events/getAllSmp",
      getSmpExportExcel: "events/getSmpExportExcel"
    }),

    formattedDate(val) {

      return dayjs(val)
        .utc()
        .format("YYYY-MM-DD HH:mm:ss");

    },

    async exportToExcel() {
      try {
        if (!this.startDate || !this.endDate) {
          this.$q.notify({
            message: 'Пожалуйста, заполните обе даты.',
            color: 'negative',
          });
          return;
        }
        console.log('startDate ', this.startDate, this.endDate)
        const response = await this.getSmpExportExcel( {
          start_date: this.startDate,
          end_date: this.endDate,
        });

        if (response.status === 200) {
          const fileName = `smp_${this.startDate}_${this.endDate}.xlsx`;
          const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
        }
      } catch (error) {
        this.$q.notify({
          message: error.message || 'Ошибка при выгрузке данных.',
          color: 'negative',
        });
      }
    },

    async onRequest(
      props,
      filter_field = ["full_name", "iin", "mobile_number"],
      filter_value
    ) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;
      let start_date = this.startDate;
      let end_date = this.endDate;
      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getAllSmp({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: filter_field,

        filter_values: filter_value
          ? [filter_value, filter_value, filter_value]
          : [filter, filter, filter],
        start_date: start_date,
        end_date: end_date

      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
<style>
.q-chip__content {
  display: block;
  text-align: center;
}
.case_text {
  color: #0e64f2;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  text-decoration-line: underline;
}

.q-table tbody td {
  font-size: 13px;
}
</style>
