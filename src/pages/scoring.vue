<template>
  <CustomTable
    :data="scoringList"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editScoringItem"
    @addRow="addScoringItem"
    :tabletitle="$t('statusSettings')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="true"
    :is_edit_disabled="!scoring.risk_level || !scoring.decision_status || !scoring.system_id || !scoring.event_type_id"
    :is_add_disabled="
      !scoring.risk_level || !scoring.decision_status || !scoring.system_id || !scoring.event_type_id
    "
    :isAddAvailable="true"
    :loading="loading"
    :filter.sync="filter"
    @deleteRow="deleteScoringItem"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:risk_level="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
             props.row.risk_level == 'Низкий' ? $t('low') : props.row.risk_level == 'Средний' ? $t('middle') : $t('high')
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>  

    <template v-slot:event_type_id="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
              eventTypeList.length
                ? convertEventType(eventTypeList.find(x => x.id == props.row.event_type_id).event_type)
                : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template> 

    <template v-slot:system_id="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
              systemList.length
                ? systemList.find(x => x.id == props.row.system_id).name_ru
                : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:decision_status="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
              statusesSystem.length
                ? convertDecision(statusesSystem.find(x => +x.key == +props.row.decision_status)
                    .value)
                : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('minimalScore')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="scoring.min_score"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('minimalScore')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('maximumScore')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="scoring.max_score"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('maximumScore')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('riskLevel')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('riskLevel')"
                  dense
                  v-model="scoring.risk_level"
                  :options="riskOptions"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('channel')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('channel')"
                  dense
                  v-model="scoring.system_id"
                  :options="systemList"
                  option-label="name_ru"
                  option-value="id"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>

             <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('eventType')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('eventType')"
                  dense
                  v-model="scoring.event_type_id"
                  :options="eventTypeList"
                  option-label="event_type"
                  option-value="id"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('decision')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('decision')"
                  dense
                  v-model="scoring.decision_status"
                  :options="statusesSystem"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('addValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('minimalScore')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="scoring.min_score"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('minimalScore')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('maximumScore')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="scoring.max_score"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('maximumScore')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('riskLevel')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('riskLevel')"
                  dense
                  v-model="scoring.risk_level"
                  :options="riskOptions"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('channel')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('channel')"
                  dense
                  v-model="scoring.system_id"
                  :options="systemList"
                  option-label="name_ru"
                  option-value="id"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>

              <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('eventType')}} 
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('eventType')"
                  dense
                  v-model="scoring.event_type_id"
                  :options="eventTypeList"
                  option-label="event_type"
                  option-value="id"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('decision')}}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('decision')"
                  dense
                  v-model="scoring.decision_status"
                  :options="statusesSystem"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      scoring: {
        decision_status: "",
        event_type_id: "",
        max_score: 0,
        min_score: 0,
        risk_level: "",
        system_id: ""
      },
      riskOptions: ["Низкий", "Средний", "Высокий"],
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  created() {
    if (!this.systemList.length) {
      this.getSystemList({ page: 1, per_page: 100 })
        .then(response => {})
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    if (!this.eventTypeList.length) {
      this.getEventTypeList({ page: 1, per_page: 100 })
        .then(response => {})
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    if (!this.statusesSystem.length) {
      this.getCaseStatusesSystem({ page: 1, per_page: 10 })
        .then(res => {})
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }

    this.getScoringList({ page: 1, per_page: 100 })
      .then(response => {})
      .catch(err => {
        this.$q.notify({
          message: err.message,
          color: "negative"
        });
      });
  },

  computed: {
    ...mapState({
      scoringList: state => state.admin.scoringList,
      systemList: state => state.admin.systemList,
      statusesSystem: state => state.admin.statusessystem,
      eventTypeList: state => state.admin.eventTypeList
    }),
     columns() {
       return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "min_score",
          label: this.$t('minimalScore'),
          field: row => row.min_score,
          align: "center"
        },
        {
          name: "max_score",
          label: this.$t('maximumScore'),
          field: row => row.max_score,
          align: "center"
        },
        {
          name: "risk_level",
          label: this.$t('riskLevel'),
          field: row => row.risk_level,
          sortable: true,
          align: "center"
        },
        {
          name: "system_id",
          label: this.$t('channel'),
          field: row => row.system_id,
          align: "center"
        },
        {
          name: "event_type_id",
          label: this.$t('eventType'),
          field: row => row.event_type_id,
          align: "center"
        },
        {
          name: "decision_status",
          label: this.$t('decision'),
          field: row => row.decision_status,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]},
  },

  methods: {
    ...mapActions({
      addScoringListItem: "admin/addScoringListItem",
      editScoringListItem: "admin/editScoringListItem",
      deleteScoringListItem: "admin/deleteScoringListItem",
      getScoringList: "admin/getScoringList",
      getSystemList: "admin/getSystemList",
      getCaseStatusesSystem: "admin/getCaseStatusesSystem",
      getEventTypeList: "admin/getEventTypeList"
    }),

    convertEventType (val) {
     if (val == 'Регистрация') {
        return this.$t('registration')
      } else if (val == 'Авторизация по логину') {
        return this.$t('loginAuthorization')
      } else if (val == 'Авторизация по моб. телефону') {
        return this.$t('loginMobilePhone') 
      } else if (val == 'Фин. операция') {
        return this.$t('finOperation')
      } else {
        return this.$t('profileChange')
      } 
    },

     convertDecision(val) {
      if (val == 'Разрешено') {
        return this.$t('allowed')
      } else if (val == 'Приостановлено') {
        return this.$t('suspended')
      } else if (val == 'Запрещено') {
        return this.$t('forbidden') 
      } else if (val == 'На анализе') {
        return this.$t('onanalys')
      } else {
        return this.$t('unabletocheck')
      }
    },

    addScoringItem() {
      let form = {};
      form = {
        ...this.scoring,
        decision_status: +this.scoring.decision_status,
        event_type_id: +this.scoring.event_type_id,
        system_id: +this.scoring.system_id,
        min_score: +this.scoring.min_score,
        max_score: +this.scoring.max_score
      };
      this.addScoringListItem(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно добавлено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    editScoringItem() {
      let form = {};
      form = {
        ...this.scoring,
        decision_status: +this.scoring.decision_status,
        event_type_id: +this.scoring.event_type_id,
        system_id: +this.scoring.system_id,
        min_score: +this.scoring.min_score,
        max_score: +this.scoring.max_score
      };
      this.editScoringListItem(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно изменено `,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    deleteScoringItem(row) {
      this.deleteScoringListItem(row.id)
        .then(response => {
          this.$q.notify({
            message: `Значение ${row.id} удалено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getScoringList({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("id,risk_level"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.scoring = {
        ...row,
        decision_status: "" + row.decision_status,
        system_id: "" + row.system_id
      };
    },

    reset() {
      this.scoring = {
        decision_status: "",
        event_type_id: "",
        max_score: 0,
        min_score: 0,
        risk_level: "",
        system_id: ""
      };
    }
  }
};
</script>
