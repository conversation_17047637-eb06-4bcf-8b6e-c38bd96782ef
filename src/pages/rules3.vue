<template>
  <CustomTable
    :data="financialLimitParams"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams3Inner"
    @addRow="addFinancialLimitParamInner"
    :tabletitle="`${$t('rule')} #3`"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="true"
    :is_edit_disabled="!timemeasure || (!rules3.amount && !rules3.operations_count) || !rules3.period || !rules3.product || !rules3.debet_credit"
    :is_add_disabled="!timemeasure || (!rules3.amount && !rules3.operations_count) || !rules3.period || !rules3.product || !rules3.debet_credit"
    :isAddAvailable="true"
    :loading="loading"
    :filter.sync="filter"
        @deleteRow="deleteFinancialLimitParamInner"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:product="{props}">
       <q-item>
        <q-item-section>
          <q-item-label>
            {{ convertProductToName(props.row.product)}}
          </q-item-label>
        </q-item-section>
      </q-item> 
    </template>

    <template v-slot:debet_credit="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{ props.row.debet_credit == 1 ? "Дебет" : "Кредит" }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:person_type="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
              props.row.person_type == 1
                ? $t("ul")
                : props.row.person_type == 2
                ? $t("fl")
                : $t("ep")
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>


    <template v-slot:period="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
             convertTimeMeasurementToLang(props.row.period)
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

     <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
       <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
               <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('operationView')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('operationView')"
                  dense
                  v-model="rules3.product"
                  :options="hdbkProducts"
                  :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                  option-value="code"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('sumLimit')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.amount"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('sumLimit')"
                />
              </q-item-section>
            </q-item>
             <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                    {{$t('debitCredit')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('debitCredit')"
                  dense
                  v-model="rules3.debet_credit"
                  :options="debetCreditOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('operationLimit')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.operations_count"
                  type="number"
                  outlined
                  :label="$t('operationLimit')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('scoreResult')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.default_score"
                  type="number"
                  outlined
                  :label="$t('scoreResult')"
                />
              </q-item-section>
            </q-item>
              <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                    {{$t('clientType')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('clientType')"
                  dense
                  v-model="rules3.person_type"
                  :options="personTypeOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('period')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.period"
                  type="number"
                  outlined
                  :label="$t('period')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('measurement')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          Добавить значение
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
               <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('operationView')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('operationView')"
                  dense
                  v-model="rules3.product"
                  :options="hdbkProducts"
                  :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                  option-value="code"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('sumLimit')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.amount"
                  outlined
                  type="number"
                  :disabled="true"
                  :label="$t('sumLimit')"
                />
              </q-item-section>
            </q-item>
             <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                    {{$t('debitCredit')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('debitCredit')"
                  dense
                  v-model="rules3.debet_credit"
                  :options="debetCreditOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('operationLimit')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.operations_count"
                  type="number"
                  outlined
                  :label="$t('operationLimit')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('scoreResult')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.default_score"
                  type="number"
                  outlined
                  :label="$t('scoreResult')"
                />
              </q-item-section>
            </q-item>
              <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                    {{$t('clientType')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('clientType')"
                  dense
                  v-model="rules3.person_type"
                  :options="personTypeOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('period')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules3.period"
                  type="number"
                  outlined
                  :label="$t('period')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('measurement')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      rules3: {
        amount: 100000,
        default_score: 100,
        operations_count: 100,
        period: "",
        person_type: "1",
        product: "",
        debet_credit: "2"
      },
      timemeasure: "",
      debetCreditOptions: [
         {
          key: "1",
          value: "Дебет"
        },
        {
          key: "2",
          value: "Кредит"
        }, 
      ],
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  created() {
    if (!this.hdbkProducts.length) {
       this.getHdbkProducts({page:1, per_page: 100})
        .then(response => {
          console.log(this.hdbkProducts)
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        }); 
    }
  },

  computed: {
    ...mapState({
      financialLimitParams: state => state.admin.financialLimitParams,
      hdbkProducts: state => state.admin.hdbkProducts
    }),
     personTypeOptions() { 
       return [
          {
          key: "1",
          value: this.$t('ul')
        },
        {
          key: "2",
          value: this.$t('fl')
        },
        {
          key: "3",
          value: this.$t('ep')
        }
      ]} ,
    timesOptions() {
      return [{
          key: "day",
          value: this.$t('day')
        },
        {
          key: "month",
          value: this.$t('month')
        }]
    },
    columns() {
      return [ {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "product",
          label: this.$t('operationView'),
          field: row => row.product,
          align: "center"
        },
        {
          name: "person_type",
          label: this.$t('clientType'),
          field: row => row.person_type,
          align: "center"
        },
        {
          name: "amount",
          label: this.$t('sumLimit'),
          field: row => row.amount,
          sortable: true,
          align: "center"
        },
        {
          name: "debet_credit",
          label: this.$t('debitCredit'),
          field: row => row.debet_credit,
          align: "center"
        },
        {
          name: "default_score",
          label: this.$t('scoreResult'),
          field: row => row.default_score,
          align: "center"
        },
        {
          name: "operations_count",
          label: this.$t('operationLimit'),
          field: row => row.operations_count,
          align: "center"
        },
        {
          name: "period",
          label: this.$t('period'),
          field: row => row.period,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }]
    }
  },

  methods: {
    ...mapActions({
      getFinancialLimitParams: "admin/getFinancialLimitParams",
      editFinancialLimitParams: "admin/editFinancialLimitParams",
      deleteFinancialLimitParams: "admin/deleteFinancialLimitParams",
      addFinancialLimitParams: "admin/addFinancialLimitParams",
      getHdbkProducts: "admin/getHdbkProducts"
    }),

    convertProductToName(product) {
      const p = this.hdbkProducts.find(x => x.code == product)
      return this.currLang == 'ru' ? p.name_ru : p.name_kz
    },

    generateForm(rules3) {
      if (+rules3.period !== 1) {
        return {
          ...rules3,
          period: rules3.period + " " + this.timemeasure + "s",
          default_score: +rules3.default_score,
          operations_count: +rules3.operations_count,
          amount: +rules3.amount,
          person_type: +rules3.person_type,
          debet_credit: +rules3.debet_credit
        };
      } else {
        return {
          ...rules3,
          period: rules3.period + " " + this.timemeasure,
          default_score: +rules3.default_score,
          operations_count: +rules3.operations_count,
          amount: +rules3.amount,
          person_type: +rules3.person_type,
          debet_credit: +rules3.debet_credit
        };
      }
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getFinancialLimitParams({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("id"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      if (row.period.includes('day') || row.period.includes('days')) {
        this.timemeasure = this.timesOptions[0].key
      } else {
        this.timemeasure = this.timesOptions[1].key
      }
      this.rules3 = { ...row, period: +row.period.match(/\d+/)[0], debet_credit: "" + row.debet_credit, person_type: "" + row.person_type };
    },

    deleteFinancialLimitParamInner(row) {
        this.deleteFinancialLimitParams(row.id)
        .then(response => {
          this.$q.notify({
            message: `Вид операций  ${row.id} удален`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    reset() {
      this.rules3 = {
        amount: 100000,
        default_score: 100,
        operations_count: 100,
        period: "",
        person_type: "1",
        product: "",
        debet_credit: "2"
      };
      this.timemeasure = ""
    },

    editParams3Inner() {
      let form = {};
      form = this.generateForm(this.rules3);
      this.editFinancialLimitParams(form)
        .then(response => {
          this.$q.notify({
            message: `Тип операций ${response.product} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    addFinancialLimitParamInner(row) {
        let form = {};
        form = this.generateForm(this.rules3); 
        this.addFinancialLimitParams(form)
        .then(response => {
          this.$q.notify({
            message: `Тип операций ${form.product} успешно добавлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

  }
};
</script>
