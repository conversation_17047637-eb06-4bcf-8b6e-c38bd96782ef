<template>
  <CustomTable
    :data="params"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams2Inner"
    :tabletitle="`${$t('rule')} #2`"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="
      (rules2.key == 'Interval' && (!rules2.value || !timemeasure)) ||
        !rules2.value
    "
    :isAddAvailable="false"
    :loading="loading"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'Interval' ? $t('timeInterval') : props.row.key == 'Count' ? $t('eventsAmount') : $t('columnScore')}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>

       <template v-slot:value="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'Interval' ? convertTimeMeasurementToLang(props.row.value) : props.row.value}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>

    <template v-slot:is_enable="{ props }">
      <q-item>
        <q-item-section>
          <!-- <q-icon name="done" v-if="props.row.is_enable"/> -->
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:send_result="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:create_case="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <!-- <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  Наименование справочника
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules2.key"
                  outlined
                  :disabled="true"
                  label="Наименование справочника"
                />
              </q-item-section>
            </q-item> -->
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules2.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none" v-if="rules2.key == 'Interval'">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('measurement')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      rules2: {
        key: "",
        name: "",
        value: ""
      },
      timemeasure: "",
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      params: state => state.admin.multipleLoginParams
    }),
    timesOptions() {
      return [{
          key: "year",
          value: this.$t('year')
        },
        {
          key: "month",
          value: this.$t('month')
        },
        {
          key: "hour",
          value: this.$t('hour')
        },
        {
          key: "minute",
          value: this.$t('minute')
        }]
    },
    columns() {
        return [
        {
          name: "key",
          label: this.$t('columnName'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getMultipleLoginParams: "admin/getMultipleLoginParams",
      editLoginParams: "admin/editLoginParams"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getMultipleLoginParams({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("key"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      if (row.key == "Interval") {
        this.rules2 = { ...row, value: +row.value.match(/\d+/)[0] };
      } else {
        this.rules2 = { ...row };
      }
    },

    reset() {
      this.rules2 = {
        key: "",
        name: "",
        value: ""
      };
    },

    generateForm(rules2) {
      if (rules2.value !== "1") {
        return {
          ...rules2,
          value: rules2.value + " " + this.timemeasure + "s"
        };
      } else {
        return {
          ...rules2,
          value: rules2.value + " " + this.timemeasure
        };
      }
    },

    editParams2Inner() {
      let form = {};

      if (this.rules2.key == 'Count' && +this.rules2.value < 2) {
        this.$q.notify({
            message: `Параметр количетсва событий должен быть, больше 2`,
            color: "red"
        }); 
        return;
      }

      if (this.rules2.key == 'DefaultScore' && (+this.rules2.value < 0 || +this.rules2.value > 100)) {
        this.$q.notify({
            message: `Скор балл должен быть в диапозоне 0-100`,
            color: "red"
        }); 
        return;
      }
      if (this.rules2.key == "Interval") {
        form = this.generateForm(this.rules2);
      } else {
        form = { ...this.rules2 };
      }



      this.editLoginParams(form)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    addScenario() {
      console.log("nothing for now");
    }
  }
};
</script>
