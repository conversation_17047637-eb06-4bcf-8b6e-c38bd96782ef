<template>
  <q-page class="q-pa-sm">
    <CustomTable
      :data="rules1params"
      :columns="columns"
      :tabletitle="`${$t('rule')} #1`"
      :isAddAvailable="false"
      :hide-no-data="true"
      :isDeleteAvailable="false"
      @onEditClick="onEditClick"
      @saveEdit="saveEdit"
    >
      <template v-slot:editModal>
        <q-card-section>
          <div class="text-h6">
            {{$t('editing')}}
            <q-btn
              round
              flat
              dense
              icon="close"
              class="float-right"
              color="grey-8"
              v-close-popup
            ></q-btn>
          </div>
        </q-card-section>
        <q-separator inset></q-separator>
        <q-card-section class="q-pt-none">
          <q-form class="q-gutter-md">
            <q-list>
              <!-- <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs"> {{$t('columnName')}} </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    outlined
                    v-model="rule.name"
                    :label="$t('columnName')"
                  />
                </q-item-section>
              </q-item> -->
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs"> {{$t('columnDescription')}} </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-if="currLang == 'ru'"
                    outlined
                    v-model="rule.description"
                    :label="$t('columnDescription')"
                  />
                   <q-input
                    class="q-mt-xs"
                    dense
                    v-else
                    outlined
                    v-model="rule.description_kz"
                    :label="$t('columnDescription')"
                  />
                </q-item-section>
              </q-item>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs"> {{$t('percentageMatch')}} </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    outlined
                    v-model="rule.match_percent"
                    :label="$t('percentageMatch')"
                    type="number"
                  />
                </q-item-section>
              </q-item>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs"> {{$t('order')}} </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    outlined
                    v-model="rule.order"
                    :label="$t('order')"
                    type="number"
                  />
                </q-item-section>
              </q-item>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-checkbox
                    class="q-ml-md"
                    dense
                    v-model="rule.is_enable"
                    :label="$t('on')"
                    color="teal"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-form>
        </q-card-section>
      </template>

      <template v-slot:is_enable="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label
              ><q-icon
                v-if="props.row.is_enable"
                name="done"
                size="sm"
                color="green"
              />
              <q-icon v-else name="cancel" color="red" size="sm" />
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:id="{ props }">
        <router-link :to="`/rules1/${props.row.id}`">
          <q-item>
            <q-item-section>
              <q-item-label>{{ props.row.id }}</q-item-label>
            </q-item-section>
          </q-item>
        </router-link>
      </template>
    </CustomTable>
    <router-view class="row3"></router-view>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      rule: {
        name: "",
        description: "",
        match_percent: 0,
        order: 0,
        is_enable: true
      }, 
    };
  },

  created() {
     this.getRulesParam1({
        page: 1,
        per_page: 5
      }).then().catch(err => console.log(err.message));
  },

  computed: {
    ...mapState({
      rules1params: state => state.admin.rules1params
    }),
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
         {
          name: "order",
          label: this.$t('order'),
          field: row => row.order,
          align: "center",
           sortable: true,
        },
        {
          name: this.currLang == 'ru' ? "description" : "description_kz",
          label: this.$t('columnDescription'),
          field: row => this.currLang == 'ru' ? row.description : row.description_kz,
          align: "center"
        },
        {
          name: "match_percent",
          label: this.$t('percentageMatch'),
          field: row => row.match_percent,
          align: "center"
        },
        {
          name: "is_enable",
          label: this.$t('on'),
          field: row => row.is_enable,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ] 
    }
  },

  methods: {
    ...mapActions({
      getRulesParam1: "admin/getRulesParam1",
      editParamsRule1: "admin/editParamsRule1"
    }),

    // async onRequest(props) {
    //   const {
    //     page,
    //     rowsPerPage,
    //     rowsNumber,
    //     sortBy,
    //     descending
    //   } = props.pagination;
    //   const filter = props.filter;

    //   this.loading = true

    //   // get all rows if "All" (0) is selected
    //   const fetchCount =
    //     rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

    //   // calculate starting row of data
    //   const startRow = (page - 1) * rowsPerPage;
      
    //   // this.pagination.rowsNumber = 
    //   await this.getRulesParam1({
    //     page: page,
    //     per_page: rowsPerPage
    //   });

    //   // don't forget to update local pagination object
    //   this.pagination.page = page;
    //   this.pagination.rowsPerPage = rowsPerPage;
    //   this.pagination.sortBy = sortBy;
    //   this.pagination.descending = descending;
    //   this.loading = false
    // },

    onEditClick(row) {
      this.rule = { ...row };
    },

    saveEdit() {
      const rule = {
        ...this.rule,
        match_percent: +this.rule.match_percent,
        order: +this.rule.order
      };
      this.editParamsRule1(rule)
        .then(response => {
          this.$q.notify({
            message: `Правило ${response.id} изменено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
