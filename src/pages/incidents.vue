<template>
  <q-page class="q-pa-lg">
    <q-card class="cardcontainer q-mt-md q-gutter-y-md">
      <div class="row">
        <span> Тип операций </span>
        <span class="q-ml-lg"> - </span>
      </div>

      <q-separator />

      <div class="row">
        <span> ID сценария: </span>
        <span class="q-ml-lg"> - </span>
      </div>

      <q-separator />

      <div class="row">
        <span> Скоринг  балл: </span>
        <span class="q-ml-lg"> - </span>
      </div>

      <q-separator />

      <div class="row">
        <span> Дата и время кейса: </span>
        <span class="q-ml-lg"> - </span>
      </div>

      <q-separator />

      <div class="row">
        <span> Событие-триггер </span>
        <span class="q-ml-lg"> - </span>
      </div>

      <q-separator />

      <div class="row">
        <span> ID сценария-треггера </span>
        <span class="q-ml-lg"> - </span>
      </div>

            <div class="row q-gutter-x-lg q-mt-lg">
        <q-btn class="button1"  label="Отмена" />
        <q-btn class="button2" label="Добавить кейс" />
      </div>

    </q-card>
    <!-- <q-dialog v-model="employee_dialog">
      <q-card class="my-card" flat bordered>
        <q-card-section>
          <div class="text-h6">
            Employee Details
            <q-btn round flat dense icon="close" class="float-right" color="grey-8" v-close-popup></q-btn>
          </div>
        </q-card-section>
        <q-card-section horizontal>
          <q-card-section class="q-pt-xs">
            <div class="text-overline">US Region</div>
            <div class="text-h5 q-mt-sm q-mb-xs">Mayank Patel</div>
            <div class="text-caption text-grey">
              Sales and Marketing Executive | Graduate and past committee | Keynote speaker on Selling and Recruiting
              Topics
            </div>
          </q-card-section>

          <q-card-section class="col-5 flex flex-center">
            <q-img
              class="rounded-borders"
              src="https://cdn.quasar.dev/img/boy-avatar.png"
            />
          </q-card-section>
        </q-card-section>

        <q-separator/>

        <q-card-section>
          Assessing clients needs and present suitable promoted products. Liaising with and persuading targeted doctors
          to prescribe our products utilizing effective sales skills.
        </q-card-section>
      </q-card>
    </q-dialog> -->
  </q-page>
</template>

<script>
import { exportFile } from "quasar";

function wrapCsvValue(val, formatFn) {
  let formatted = formatFn !== void 0 ? formatFn(val) : val;

  formatted =
    formatted === void 0 || formatted === null ? "" : String(formatted);

  formatted = formatted.split('"').join('""');

  return `"${formatted}"`;
}

export default {
  data() {
    return {
      filter: "",
      mode: "list",
      invoice: {},
      employee_dialog: false,
      columns: [
        {
          name: "serial_no",
          align: "left",
          label: "Serial No.",
          field: "serial_no",
          sortable: true
        },
        {
          name: "emp_id",
          required: true,
          label: "Employee Id",
          align: "left",
          field: "emp_id",
          sortable: true
        },
        {
          name: "name",
          align: "left",
          label: "Employee Name",
          field: "name",
          sortable: true
        },
        {
          name: "salary_type",
          align: "left",
          label: "Salary Type",
          field: "salary_type",
          sortable: true
        },
        {
          name: "basic_salary",
          align: "left",
          label: "Basic salary",
          field: "basic_salary",
          sortable: true
        },
        {
          name: "overtime",
          align: "left",
          label: "Overtime",
          field: "overtime",
          sortable: true
        },
        {
          name: "detail",
          align: "left",
          label: "Detail",
          field: "detail",
          sortable: true
        },
        {
          name: "action",
          align: "left",
          label: "Action",
          field: "action",
          sortable: true
        }
      ],
      data: [
        {
          serial_no: "01",
          emp_id: "Emp 233",
          name: "Leslie Tecklenburg",
          basic_salary: "$ 4200",
          salary_type: "Basic",
          overtime: "$ 20"
        },
        {
          serial_no: "02",
          emp_id: "Emp 104",
          name: "Lia Whitledge",
          basic_salary: "$ 2550",
          salary_type: "Basic",
          overtime: "$ 40"
        },
        {
          serial_no: "03",
          emp_id: "Emp 345",
          name: "Sam Wileman",
          basic_salary: "$ 3800",
          salary_type: "Basic",
          overtime: "$ 90"
        },
        {
          serial_no: "04",
          emp_id: "Emp 345",
          name: "Edgar Colmer",
          basic_salary: "$ 4000",
          salary_type: "Basic",
          overtime: "$ 56"
        },
        {
          serial_no: "05",
          emp_id: "Emp 895",
          name: "Kaiden Rozelle",
          basic_salary: "$ 3200",
          salary_type: "Basic",
          overtime: "$ 23"
        }
      ],
      pagination: {
        rowsPerPage: 10
      }
    };
  },
  methods: {
    exportTable() {
      // naive encoding to csv format
      const content = [this.columns.map(col => wrapCsvValue(col.label))]
        .concat(
          this.data.map(row =>
            this.columns
              .map(col =>
                wrapCsvValue(
                  typeof col.field === "function"
                    ? col.field(row)
                    : row[col.field === void 0 ? col.name : col.field],
                  col.format
                )
              )
              .join(",")
          )
        )
        .join("\r\n");

      const status = exportFile(
        "employee_salary_list.csv",
        content,
        "text/csv"
      );

      if (status !== true) {
        this.$q.notify({
          message: "Browser denied file download...",
          color: "negative",
          icon: "warning"
        });
      }
    }
  }
};
</script>
<style>
.cardcontainer {
  border-radius: 16px;
  padding: 24px;
}
.q-chip__content {
  display: block;
  text-align: center;
}
.text1 {
  letter-spacing: -0.3px;

  /* Basic / Black */

  color: #12022f;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
}

.button1 {
  background: #d2ab66;
  border-radius: 4px;
  height: 42px;
  color: white;
  width: 265px;
font-style: normal;
font-weight: normal;
}

.button2 {
  height: 42px;
  background: #17335d;
  border-radius: 4px;
  color: white;
   width: 265px;
font-style: normal;
font-weight: normal;
}
</style>
