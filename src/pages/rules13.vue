<template>
  <CustomTable
    :data="params"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams11Inner"
    :tabletitle="`${$t('rule')} #13`"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="
      (rules13.key == 'Interval' && (!rules13.value || !timemeasure)) ||
        !rules13.value
    "
    :isAddAvailable="false"
    :loading="loading"
  >

    <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ $t(`newCardTransaction.${props.row.key}`) || props.row.key}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>

       <template v-slot:value="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'Interval' ? convertTimeMeasurementToLang(props.row.value) : props.row.value}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>


    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules13.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none" v-if="rules13.key == 'Interval'">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('measurement')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      rules13: {
        key: "",
        name: "",
        value: ""
      },
      timemeasure: "",
      pagination: {
        sortBy: "key",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      params: state => state.admin.newCardTransactionParams
    }),
    columns() {
      return[
        {
          name: "key",
          label: this.$t('columnName'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    },
    timesOptions() {
      return [
        {
          key: "year",
          value: this.$t('year')
        },
        {
          key: "month",
          value: this.$t('month')
        },
        {
          key: "hour",
          value: this.$t('hour')
        },
        {
          key: "minute",
          value: this.$t('minute')
        },
      ]
    }
  },

  methods: {
    ...mapActions({
      getPaymentTrendParams: "admin/getNewCardTransactionParams",
      editPaymentTrendParams: "admin/editNewCardTransactionParams"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getPaymentTrendParams({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("key"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      if (row.key == "Interval") {
        this.rules13 = { ...row, value: +row.value.match(/\d+/)[0] };
      } else {
        this.rules13 = { ...row };
      }
    },

    reset() {
      this.rules13 = {
        key: "",
        name: "",
        value: ""
      };
    },

    generateForm(rules13) {
      if (rules13.value !== "1") {
        return {
          ...rules13,
          value: rules13.value + " " + this.timemeasure + "s"
        };
      } else {
        return {
          ...rules13,
          value: rules13.value + " " + this.timemeasure
        };
      }
    },

    editParams11Inner() {
      let form = {};

      if (this.rules13.key == 'DefaultScore' && (+this.rules13.value < 0 || +this.rules13.value > 100)) {
        this.$q.notify({
            message: `Скор балл должен быть в диапозоне 0-100`,
            color: "red"
        }); 
        return;
      }

      if (this.rules13.key == "Interval") {
        form = this.generateForm(this.rules13);
      } else {
        form = { ...this.rules13 };
      }



      this.editPaymentTrendParams(form)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },
  }
};
</script>
