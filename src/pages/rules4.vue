<template>
  <CustomTable
    :data="params"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams4Inner"
    @addRow="addFinancialSpeedPayment"
    :tabletitle="`${$t('rule')} #4`"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="true"
    :is_edit_disabled="
      !timemeasure ||
        !rules4.operations_count ||
        !rules4.period ||
        !rules4.product
    "
    :isAddAvailable="
      !timemeasure ||
        !rules4.operations_count ||
        !rules4.period ||
        !rules4.product
    "
    :loading="loading"
    :filter.sync="filter"
    @deleteRow="deleteFinancialSpeedPaymentParamInner"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:product="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{ convertProductToName(props.row.product) }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

      <template v-slot:period="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
             convertTimeMeasurementToLang(props.row.period)
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{ $t("addValue") }}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("operationView") }}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('operationView')"
                  dense
                  v-model="rules4.product"
                  :options="hdbkProducts"
                  :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                  option-value="code"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("operationLimit") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.operations_count"
                  type="number"
                  outlined
                  :label="$t('operationLimit')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("scoreResult") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.default_score"
                  type="number"
                  outlined
                  :label="$t('scoreResult')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("period") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.period"
                  type="number"
                  outlined
                  :label="$t('period')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("measurement") }}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{ $t("changeValue") }}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("operationView") }}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('operationView')"
                  dense
                  v-model="rules4.product"
                  :options="hdbkProducts"
                  :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                  option-value="code"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("operationLimit") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.operations_count"
                  type="number"
                  outlined
                  :label="$t('operationLimit')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("scoreResult") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.default_score"
                  type="number"
                  outlined
                  :label="$t('scoreResult')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("period") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules4.period"
                  type="number"
                  outlined
                  :label="$t('period')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("measurement") }}
                </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  created() {
    if (!this.hdbkProducts.length) {
      this.getHdbkProducts({ page: 1, per_page: 100 })
        .then(response => {
          console.log(this.hdbkProducts);
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  },

  data() {
    return {
      rules4: {
        default_score: 100,
        operations_count: 100,
        period: "",
        product: ""
      },
      timemeasure: "",
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      params: state => state.admin.financialSpeedPaymentParams,
      hdbkProducts: state => state.admin.hdbkProducts
    }),
    timesOptions() {
      return [
        {
          key: "hour",
          value: this.$t("hour")
        },
        {
          key: "minute",
          value: this.$t("minute")
        }
      ];
    },
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "product",
          label: this.$t("operationView"),
          field: row => row.product,
          sortable: true,
          align: "center"
        },
        {
          name: "operations_count",
          label: this.$t("operationLimit"),
          field: row => row.operations_count,
          sortable: true,
          align: "center"
        },
        {
          name: "default_score",
          label: this.$t("scoreResult"),
          field: row => row.default_score,
          sortable: true,
          align: "center"
        },
        {
          name: "period",
          label: this.$t("period"),
          field: row => row.period,
          sortable: true,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    }
  },

  methods: {
    ...mapActions({
      getFinancialSpeedPaymentParams: "admin/getFinancialSpeedPaymentParams",
      editFinancialSpeedPaymentParams: "admin/editFinancialSpeedPaymentParams",
      getHdbkProducts: "admin/getHdbkProducts",
      addFinancialSpeedPaymentParams: "admin/addFinancialSpeedPaymentParams",
      deleteFinancialSpeedPaymentParam: "admin/deleteFinancialSpeedPaymentParam"
    }),

    convertProductToName(product) {
      const p = this.hdbkProducts.find(x => x.code == product);
      return this.currLang == "ru" ? p.name_ru : p.name_kz;
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getFinancialSpeedPaymentParams({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("product"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      if (row.period.includes("hour") || row.period.includes("hours")) {
        this.timemeasure = this.timesOptions[0].key;
      } else {
        this.timemeasure = this.timesOptions[1].key;
      }
      this.rules4 = { ...row, period: +row.period.match(/\d+/)[0] };
    },

    reset() {
      this.rules4 = {
        default_score: 100,
        operations_count: 100,
        period: "",
        product: ""
      };
      this.timemeasure = "";
    },

    generateForm(rules4) {
      if (+rules4.period !== 1) {
        return {
          ...rules4,
          period: rules4.period + " " + this.timemeasure + "s",
          default_score: +rules4.default_score,
          operations_count: +rules4.operations_count
        };
      } else {
        return {
          ...rules4,
          period: rules4.period + " " + this.timemeasure,
          default_score: +rules4.default_score,
          operations_count: +rules4.operations_count
        };
      }
    },

    deleteFinancialSpeedPaymentParamInner(row) {
      this.deleteFinancialSpeedPaymentParam(row.id)
        .then(response => {
          this.$q.notify({
            message: `Тип операций  ${row.id} удален`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    editParams4Inner() {
      let form = {};
      form = this.generateForm(this.rules4);
      this.editFinancialSpeedPaymentParams(form)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.id} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    addFinancialSpeedPayment() {
      let form = {};
      form = this.generateForm(this.rules4);
      this.addFinancialSpeedPaymentParams(form)
        .then(response => {
          this.$q.notify({
            message: `Тип операций ${form.product} успешно добавлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
  }
};
</script>
