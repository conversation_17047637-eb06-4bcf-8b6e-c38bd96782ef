<template>
  <div>
    <CustomTable
      :data="params"
      :columns="columns"
      @reset="reset"
      @onEditClick="onEditClick"
      @saveEdit="editParams9Inner"
      :tabletitle="`${$t('rule')} #9`"
      :pagination.sync="pagination"
      @onRequest="onRequest"
      @onRefresh="onRequest({ filter, pagination })"
      :isDeleteAvailable="false"
      :is_edit_disabled="
        (rules9.key == 'Interval' && (!rules9.value || !timemeasure)) ||
          !rules9.value
      "
      :isAddAvailable="false"
      :loading="loading"
    >
      <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{
              props.row.key == "DefaultScore"
                ? $t("columnScore")
                : $t("timeInterval")
            }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>

         <template v-slot:value="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'Interval' ? convertTimeMeasurementToLang(props.row.value) : props.row.value}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>

      <template v-slot:editModal>
        <q-card-section>
          <div class="text-h6">
            {{ $t("changeValue") }}
            <q-btn
              round
              flat
              dense
              icon="close"
              class="float-right"
              color="grey-8"
              v-close-popup
            ></q-btn>
          </div>
        </q-card-section>
        <q-separator inset></q-separator>
        <q-card-section class="q-pt-none">
          <q-form class="q-gutter-md">
            <q-list>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("value") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="rules9.value"
                    type="number"
                    outlined
                    :label="$t('value')"
                  />
                </q-item-section>
              </q-item>
              <q-item class="q-px-none" v-if="rules9.key == 'Interval'">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("measurement") }}
                  </q-item-label>
                  <q-select
                    outlined
                    class="q-mt-xs"
                    :label="$t('measurement')"
                    dense
                    v-model="timemeasure"
                    :options="timesOptions"
                    option-label="value"
                    option-value="key"
                    emit-value
                    map-options
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-form>
        </q-card-section>
      </template>
    </CustomTable>

    <CustomTable
      :data="blackListCards"
      :columns="columns2"
      @reset="reset2"
      @onEditClick="onEditClick2"
      @saveEdit="editBlackListCardInner"
      @addRow="addBlackListCardInner"
      :tabletitle="$t('blackCardListSettings')"
      :pagination.sync="pagination2"
      @onRequest="onRequest2"
      @onRefresh="onRequest2({ filter, pagination })"
      :isDeleteAvailable="true"
      :is_edit_disabled="
        !cardblacklist.card_number ||
          !cardblacklist.iinbin ||
          !cardblacklist.card_owner_name
      "
      :is_add_disabled="
        !cardblacklist.card_number ||
          !cardblacklist.iinbin ||
          !cardblacklist.card_owner_name
      "
      :isAddAvailable="true"
      :loading="loading2"
      :filter.sync="filter2"
      @deleteRow="deleteBlackListCardInner"
    >
      <template v-slot:searchfield="{ props }">
        <q-input
          outlined
          dense
          debounce="300"
          v-model="filter2"
          :placeholder="$t('search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:editModal>
        <q-card-section>
          <div class="text-h6">
            {{ $t("changeValue") }}
            <q-btn
              round
              flat
              dense
              icon="close"
              class="float-right"
              color="grey-8"
              v-close-popup
            ></q-btn>
          </div>
        </q-card-section>
        <q-separator inset></q-separator>
        <q-card-section class="q-pt-none">
          <q-form class="q-gutter-md">
            <q-list>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("cardNumber") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.card_number"
                    outlined
                    type="text"
                    :label="$t('cardNumber')"
                  />
                </q-item-section>
              </q-item>

              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("iinbin") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.iinbin"
                    outlined
                    type="text"
                    :label="$t('iinbin')"
                  />
                </q-item-section>
              </q-item>

              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("cardOwner") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.card_owner_name"
                    outlined
                    type="text"
                    :label="$t('cardOwner')"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-form>
        </q-card-section>
      </template>

      <template v-slot:addModal>
        <q-card-section>
          <div class="text-h6">
            {{ $t("addValue") }}
            <q-btn
              round
              flat
              dense
              icon="close"
              class="float-right"
              color="grey-8"
              v-close-popup
            ></q-btn>
          </div>
        </q-card-section>
        <q-separator inset></q-separator>
        <q-card-section class="q-pt-none">
          <q-form class="q-gutter-md">
            <q-list>
              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("cardNumber") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.card_number"
                    outlined
                    type="text"
                    :label="$t('cardNumber')"
                  />
                </q-item-section>
              </q-item>

              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("iinbin") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.iinbin"
                    outlined
                    type="text"
                    :label="$t('iinbin')"
                  />
                </q-item-section>
              </q-item>

              <q-item class="q-px-none">
                <q-item-section>
                  <q-item-label class="q-pb-xs">
                    {{ $t("cardOwner") }}
                  </q-item-label>
                  <q-input
                    class="q-mt-xs"
                    dense
                    v-model="cardblacklist.card_owner_name"
                    outlined
                    type="text"
                    :label="$t('cardOwner')"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-form>
        </q-card-section>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      timemeasure: "",

      rules9: {
        key: "",
        name: "",
        value: ""
      },

      cardblacklist: {
        card_number: "",
        iinbin: "",
        card_owner_name: ""
      },

      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      pagination2: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      loading2: false,
      filter: "",
      filter2: ""
    };
  },

  computed: {
    ...mapState({
      params: state => state.admin.paymentProfileParams,
      blackListCards: state => state.admin.blackListCards
    }),

    columns() {
      return [
        {
          name: "key",
          label: this.$t("columnName"),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t("value"),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    columns2() {
      return [
        {
          name: "card_number",
          label: this.$t("cardNumber"),
          field: row => row.card_number,
          sortable: true,
          align: "center"
        },
        {
          name: "iinbin",
          label: this.$t("iinbin"),
          field: row => row.iinbin,
          align: "center"
        },
        {
          name: "card_owner_name",
          label: this.$t("cardOwner"),
          field: row => row.card_owner_name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    timesOptions() {
      return [
        {
          key: "year",
          value: this.$t("year")
        },
        {
          key: "month",
          value: this.$t("month")
        },
        {
          key: "hour",
          value: this.$t("hour")
        },
        {
          key: "minute",
          value: this.$t("minute")
        }
      ];
    }
  },

  methods: {
    ...mapActions({
      getPaymentProfileParams: "admin/getPaymentProfileParams",
      editPaymentProfileParams: "admin/editPaymentProfileParams",
      getBlackListCards: "admin/getBlackListCards",
      addBlackListCard: "admin/addBlackListCard",
      editBlackListCard: "admin/editBlackListCard",
      deleteBlackListCard: "admin/deleteBlackListCard"
    }),

    async onRequest2(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading2 = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination2.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination2.rowsNumber = await this.getBlackListCards({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("iinbin,card_owner_name"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination2.page = page;
      this.pagination2.rowsPerPage = rowsPerPage;
      this.pagination2.sortBy = sortBy;
      this.pagination2.descending = descending;
      this.loading2 = false;
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getPaymentProfileParams({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("key"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      if (row.key == "Interval") {
        this.rules9 = { ...row, value: +row.value.match(/\d+/)[0] };
      } else {
        this.rules9 = { ...row };
      }
    },

    onEditClick2(row) {
      this.cardblacklist = {
        ...row
      };
    },

    reset2() {
      this.cardblacklist = {
        card_number: "",
        iinbin: "",
        card_owner_name: ""
      };
    },

    reset() {
      this.rules9 = {
        key: "",
        name: "",
        value: ""
      };
    },

    addBlackListCardInner() {
      let form = { ...this.cardblacklist };
      this.addBlackListCard(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно добавлено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    editBlackListCardInner() {
      let form = { ...this.cardblacklist };
      this.editBlackListCard(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно изменено `,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    deleteBlackListCardInner(row) {
      this.deleteBlackListCard(row.id)
        .then(response => {
          this.$q.notify({
            message: `Значение ${row.id} удалено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    generateForm(rules9) {
      if (rules9.value !== "1") {
        return {
          ...rules9,
          value: rules9.value + " " + this.timemeasure + "s"
        };
      } else {
        return {
          ...rules9,
          value: rules9.value + " " + this.timemeasure
        };
      }
    },

    editParams9Inner() {
      let form = {};

      if (
        this.rules9.key == "DefaultScore" &&
        (+this.rules9.value < 0 || +this.rules9.value > 100)
      ) {
        this.$q.notify({
          message: `Скор балл должен быть в диапозоне 0-100`,
          color: "red"
        });
        return;
      }

      if (this.rules9.key == "Interval") {
        form = this.generateForm(this.rules9);
      } else {
        form = { ...this.rules9 };
      }

      this.editPaymentProfileParams(form)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
  }
};
</script>
