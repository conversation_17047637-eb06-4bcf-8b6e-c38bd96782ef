<template>
  <CustomTable
    :data="phonelist"
    :columns="columns"
    @reset="reset"
    :tabletitle="$t('phoneNumbersBlackList')"
    :isDeleteAvailable="true"
    :is_add_disabled="!validatePhoneNumber(phone.phone)"
    :is_edit_disabled="!validatePhoneNumber(phone.phone)"
    :isAddAvailable="true"
    :isEditAvailable="true"
    @addRow="addPhoneBlackListInner"
    @deleteRow="deletePhoneBlackListInner"
    @saveEdit="saveEdit"
    @onEditClick="onEditClick"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    :loading="loading"
              @onRefresh="onRequest({filter, pagination})"
                  :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('editPhoneNumber')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('phone')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="phone.phone"
                  outlined
                  label="Пример: 87079911191, 77007771111"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="phone.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
                  {{$t('addPhoneNumber')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('phone')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="phone.phone"
                  outlined
                  label="Пример: 87079911191, 77007771111"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="phone.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
       pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      phone: {
        phone: "",
        note: ""
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      phonelist: state => state.admin.phonelist
    }),
    columns() {
      return  [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "phone",
          label: this.$t('phone'),
          field: row => row.phone,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getPhonesBlackList: "admin/getPhonesBlackList",
      addPhoneBlackList: "admin/addPhoneBlackList",
      deletePhoneBlackList: "admin/deletePhoneBlackList",
      editPhoneBlackList: "admin/editPhoneBlackList"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;
      
        this.pagination.rowsNumber = await this.getPhonesBlackList({
          page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "id,phone"
        ),
        filter_values: filter + "," + filter
      });


      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
       this.loading = false
    },
  

    onEditClick(row) {
      this.phone = { ...row };
    },

    saveEdit() {
      this.editPhoneBlackList(this.phone)
        .then(response => {
          this.$q.notify({
            message: `Телефон ${response.id} изменен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    validatePhoneNumber(phone) {
      let regexp = /^(7|8){1}?[\s\-]?\(?[7][0-9]{2}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{2}$/gm;
      if (regexp.test(phone)) {
        return true;
      }
      return false;
    },

    reset() {
      this.phone = {
        phone: "",
        note: ""
      };
    },

    deletePhoneBlackListInner(row) {
      this.deletePhoneBlackList(row.id)
        .then(response => {
          this.$q.notify({
            message: `Телефон ${row.id} удален из черного списка`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addPhoneBlackListInner() {
      this.addPhoneBlackList(this.phone)
        .then(response => {
          this.$q.notify({
            message: `Телефон ${this.phone.phone} добавлен в черный список`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
