<template>
<div>
    <CustomTable
    :data="params"
    :columns="columnsparams"
    @reset="resetParams"
    @onEditClick="onEditClick2"
    @saveEdit="editParams7Inner"
    :tabletitle="`${$t('rule')} #7`"
    :pagination.sync="pagination"
    @onRequest="onRequestParams"
    @onRefresh="onRequestParams({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="
      rules7.key == 'DefaultScore' && !rules7.value
    "
    :isAddAvailable="false"
    :loading="loading"
  >

    <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'DefaultScore' ? $t('columnScore') : $t('percent')}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>


    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules7params.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  created() {
    if (!this.hdbkProducts.length) {
      this.getHdbkProducts({ page: 1, per_page: 100 })
        .then(response => {
          console.log(this.hdbkProducts);
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  },

  data() {
    return {
      rules7params: {
        key: "",
        name: "",
        value: ""
      },
      rules7: {
        product: "",
        percent: ""
      },
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      percents: state => state.admin.notTypicalAmountPercent,
      hdbkProducts: state => state.admin.hdbkProducts,
      params: state => state.admin.notTypicalAmountParams,
    }),
    columnsparams() {
     return [
        {
          name: "key",
          label: this.$t('columnName'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getNotTypicalAmountServicePercent: "admin/getNotTypicalAmountServicePercent",
      editNotTypicalAmountServicePercent: "admin/editNotTypicalAmountServicePercent",
      getHdbkProducts: "admin/getHdbkProducts",
      addNotTypicalAmountServicePercent: "admin/addNotTypicalAmountServicePercent",
      deleteNotTypicalAmountServicePercent: "admin/deleteNotTypicalAmountServicePercent",
      getNotTypicalAmountServiceParams: "admin/getNotTypicalAmountServiceParams",
      editNotTypicalAmountServiceParams: "admin/editNotTypicalAmountServiceParams"
    }),

    findProduct(product) {
      let p = this.hdbkProducts.find(x => x.code == product) 
      if (p) {
        return p.name_ru
      }
      return product
    },

      async onRequestParams(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getNotTypicalAmountServiceParams({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("key"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getNotTypicalAmountServicePercent({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("product"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick1(row) {
      this.rules7 = { ...row};
    },

    onEditClick2(row) {
      this.rules7params = {...row}
    },

    reset() {
       this.rules7 = {
        percent: "",
        product: ""
      }
    },

    resetParams() {
      this.rules7params = {
        key: "",
        name: "",
        value: ""
      }
    },

    editParams7Inner() {

      if (this.rules7params.key == 'DefaultScore' && (+this.rules7params.value < 0 || +this.rules7params.value > 100)) {
        this.$q.notify({
            message: `Скор балл должен быть в диапозоне 0-100`,
            color: "red"
        }); 
        return;
      }

      this.editNotTypicalAmountServiceParams(this.rules7params)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    deleteNotTypicalAmountServicePercentInner(row) {
        this.deleteNotTypicalAmountServicePercent(row.id)
        .then(response => {
          this.$q.notify({
            message: `Тип операций  ${row.id} удален`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        })
    },

    editNotTypicalAmountServicePercentInner() {
       let form = {};
        form = {
            ...this.rules7,
            percent: +this.rules7.percent
        }
      this.editNotTypicalAmountServicePercent(form)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.id} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    addNotTypicalAmountServicePercentInner() {
       let form = {};
        form = {
            ...this.rules7,
            percent: +this.rules7.percent
        }
        this.addNotTypicalAmountServicePercent(form)
        .then(response => {
          this.$q.notify({
            message: `Тип операций ${form.product} успешно добавлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        }); 
    }
  }
};
</script>
