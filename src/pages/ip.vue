<template>
  <CustomTable
    :data="iplist"
    :columns="columns"
    @reset="reset"
    :tabletitle="$t('ipadressblacklist')"
    :isDeleteAvailable="true"
    :is_add_disabled="!validateIPaddress(ip.ip)" 
    :is_edit_disabled="!validateIPaddress(ip.ip)"
    :isAddAvailable="true"
    :isEditAvailable="true"
    @addRow="addIpBlackListInner"
    @deleteRow="deleteIpBlackListInner"
    @saveEdit="saveEdit"
    @onEditClick="onEditClick"
     :pagination.sync="pagination"
    @onRequest="onRequest"
        :loading="loading"
                  @onRefresh="onRequest({filter, pagination})"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeIp')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('ipAddress')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="ip.ip"
                  outlined
                  label="Пример: ***************"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="ip.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('addIp')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('ipAddress')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="ip.ip"
                  outlined
                  label="Пример: *************** либо ***********"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="ip.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      ip: {
        ip: "",
        note: ""
      },
       pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      iplist: state => state.admin.iplist
    }),
    columns() {
      return [
        {
           name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "ip",
          label: "IP",
          field: row => row.ip,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getIpBlackList: "admin/getIpBlackList",
      addIpBlackList: "admin/addIpBlackList",
      deleteIpBlackList: "admin/deleteIpBlackList",
      editIpBlackList: "admin/editIpBlackList"
    }),

     async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber  = await this.getIpBlackList({
          page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "id,ip"
        ),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false
    },

    onEditClick(row) {
      this.ip = {...row}
    },

    saveEdit() {
     this.editIpBlackList(this.ip)
        .then(response => {
          this.$q.notify({
            message: `IP ${response.id} изменен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        }); 
    },

    validateIPaddress(ipaddress) {
      if (
        /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(
          ipaddress
        )
      ) {
        return true;
      }
      return false
    },

    reset() {
      this.ip = {
        ip: "",
        note: ""
      };
    },

    deleteIpBlackListInner(row) {
      this.deleteIpBlackList(row.id)
        .then(response => {
          this.$q.notify({
            message: `IP ${row.id} удален из черного списка`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addIpBlackListInner() {
      this.addIpBlackList(this.ip)
        .then(response => {
          this.$q.notify({
            message: `IP ${this.ip.ip} добавлен в черный список`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
