<template>
  <CustomTable
    :data="scenarios"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editScenarioInner"
    tabletitle="Правила"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :loading="loading"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:description="{ props }">
      <router-link :to="`/rules${props.row.id}`">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.description }}</q-item-label>
          </q-item-section>
        </q-item>
      </router-link>
    </template>

    <template v-slot:description_kz="{ props }">
      <router-link :to="`/rules${props.row.id}`">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.description_kz }}</q-item-label>
          </q-item-section>
        </q-item>
      </router-link>
    </template>

    <template v-slot:is_enable="{ props }">
      <q-item>
        <q-item-section>
          <!-- <q-icon name="done" v-if="props.row.is_enable"/> -->
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:send_result="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:create_case="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{ $t("changeScenario") }}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{ $t("scenarioName") }}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-if="currLang !== 'ru'"
                  v-model="scenario.description_kz"
                  outlined
                  :label="$t('scenarioName')"
                />
                <q-input
                  class="q-mt-xs"
                  dense
                  v-else
                  v-model="scenario.description"
                  outlined
                  :label="$t('scenarioName')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-checkbox
                  dense
                  v-model="scenario.is_enable"
                  :label="$t('onOrOff')"
                  color="teal"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      scenario: {
        description: "",
        description_kz: "",
        is_enable: true,
      },
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 10,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      scenarios: state => state.admin.scenarios
    }),
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: this.currLang == 'ru' ? "description" : "description_kz",
          label: this.$t("scenarioName"),
          field: row => this.currLang == 'ru' ? row.description : row.description_kz,
          align: "center"
        },
        {
          name: "is_enable",
          label: this.$t("onOrOff"),
          field: row => row.is_enable,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    }
  },

  methods: {
    ...mapActions({
      getAllScenarios: "admin/getAllScenarios",
      editScenario: "admin/editScenario"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getAllScenarios({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: "name",
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.scenario = { ...row };
    },

    reset() {
      this.scenario = {
        name: "",
        is_enable: true,
        send_result: true,
        create_case: true
      };
    },

    editScenarioInner() {
      this.editScenario({
        ...this.scenario
      })
        .then(response => {
          this.$q.notify({
            message: `Сценарий ${response.id} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addScenario() {
      console.log("nothing for now");
    }
  }
};
</script>
