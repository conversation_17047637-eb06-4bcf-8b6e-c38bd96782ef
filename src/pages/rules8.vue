<template>
<div>
  <CustomTable
    :data="transferLimitClientTypes"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams8Inner"
    :tabletitle="`${$t('rule')} #8`"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="
            rules8.key == 'DefaultScore' && !rules8.value
    "
    :isAddAvailable="false"
    :loading="loading"
  >

    <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == '1' ? $t('ul') : props.row.key == '2' ? $t('fl') : $t('ep')}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>


    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules8.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>

          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>

  <CustomTable
    :data="transferLimitScore"
    :columns="columns2"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editParams8Inner"
    :tabletitle="$t('scoreResult')"
    :pagination.sync="pagination"
    :isDeleteAvailable="false"
    :is_edit_disabled="
            rules8.key == 'DefaultScore' && !rules8.value
    "
    :isAddAvailable="false"
    :loading="loading"
  >

    <template v-slot:key="{ props }">
        <q-item>
          <q-item-section>
            <q-item-label>{{ props.row.key == 'DefaultScore' ? $t('scoreResult') : ''}}</q-item-label>
          </q-item-section>
        </q-item>
    </template>


    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="rules8.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      rules8: {
        key: "",
        name: "",
        value: ""
      },
      timemeasure: "",
      pagination: {
        sortBy: "key",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapGetters({
        transferLimitScore: 'admin/getTransferLimitScore',
        transferLimitClientTypes: 'admin/getTrasnferLimitClientType'
    }),
    columns() {
      return [ {
          name: "key",
          label: this.$t('clientType'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('receiversCount'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }] 
    },

    columns2() {
      return [
         {
          name: "key",
          label: this.$t('columnName'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getTransferLimitParams: "admin/getTransferLimitParams",
      editTransferLimitParams: "admin/editTransferLimitParams"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getTransferLimitParams({
        page: page,
        per_page: rowsPerPage,
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
        this.rules8 = { ...row };
    },

    reset() {
      this.rules8 = {
        key: "",
        name: "",
        value: ""
      };
    },

    editParams8Inner() {
      if (this.rules8.key == 'DefaultScore' && (+this.rules8.value < 0 || +this.rules8.value > 100)) {
        this.$q.notify({
            message: `Скор балл должен быть в диапозоне 0-100`,
            color: "red"
        }); 
        return;
      }

      this.editTransferLimitParams(this.rules8)
        .then(response => {
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },
  }
};
</script>
