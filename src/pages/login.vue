<template>
  <q-layout>
    <q-page-container>
      <q-page class="flex flex-center">
        <div
          id="particles-js"
          :class="$q.dark.isActive ? 'dark_gradient' : 'normal_gradient'"
        ></div>

        <q-card
          v-if="currform == 'login'"
          class="login-form"
          v-bind:style="
            $q.platform.is.mobile ? { width: '80%' } : { width: '30%' }
          "
        >
          <!-- <q-img src="/statics/images/pharmacy.jpg"></q-img> -->
          <q-card-section>
            <div class="row no-wrap items-center">
              <div class="col text-h6 ellipsis">
                Авторизация
              </div>
            </div>
          </q-card-section>
          <q-card-section>
            <form class="q-gutter-md" @submit.prevent="loginNotify">
              <q-input filled v-model="email" label="Логин" lazy-rules />

              <q-input
                                      :type="isPwd ? 'password' : 'text'"
                filled
                v-model="password"
                label="Пароль"
                lazy-rules
                   :rules="[
                        val => !!val || 'Поле обязательное',
                      ]"
                bottom-slots
                :error="loginError"
              >
                 <template v-slot:error>
                    {{loginErrorText}}
      </template>
                     <template v-slot:append>
                        <q-icon
                          :name="isPwd ? 'visibility_off' : 'visibility'"
                          class="cursor-pointer"
                          @click="isPwd = !isPwd"
                        />
                      </template>
              </q-input>

              <div class="column ">
                <q-checkbox size="xs" v-model="rememberme" val="xs" label="Запомнить меня" />
              </div>

              <div class="column justify-center">
                <q-btn
                  label="Вход"
                  type="submit"
                  color="primary"
                  :disable="!email || !password"
                />

                <div class="q-mt-md row justify-center">
                  <span class="resetpasswordtext" @click="currform = 'resetpassemail'"> Восстановить пароль </span>
                </div>
              </div>
            </form>
          </q-card-section>
        </q-card>

        <q-card   class="login-form"
          v-bind:style="
            $q.platform.is.mobile ? { width: '80%' } : { width: '30%' }
          " v-if="currform == 'resetpassemail'">
           <q-card-section>
            <div class="row no-wrap items-center">

              <q-icon name="arrow_back" size="sm" style="cursor: pointer" @click="returnToLogin"/>
              <div class="col text-h6 ellipsis q-ml-sm">
                Введите почту
              </div>
            </div>
          </q-card-section>
          <q-card-section>
            <q-form >
              <q-input filled v-model="emailReset" label="Почта" lazy-rules bottom-slots
                :error="emailResetError">
                   <template v-slot:error>
                    {{emailResetErrorText}}
                  </template>
              </q-input>

              <div class="row ">
                <q-btn
                  label="Отправить"
                  type="button"
                  color="primary"
                  class="col q-mt-sm"
                  :disable="!emailReset"
                  @click="emailSend"
                />
              </div>
            </q-form>
          </q-card-section> 
        </q-card>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script type="text/javascript"></script>
<script>
import { mapActions } from "vuex";

export default {
  data() {
    return {
      email: localStorage.getItem('email') || "",
      password: "",
      isPwd: true,
      loginError: false,
      currform: "login",
      loginErrorText: "",
      emailReset: "",
      emailResetError: false,
      emailResetErrorText: "",
      rememberme: localStorage.getItem('rememberme') ? true : false,
    };
  },
  methods: {

    ...mapActions({
      login: 'auth/login',
      resetPassword: 'auth/resetPassword',
    }),

    emailSend() {
      this.resetPassword(this.emailReset).then(response => {
        if (response) {
          this.emailResetError = false
          this.$q.notify({
          message: `Временный пароль отправлен на почту ${this.emailReset}`,
          color: 'green'
        })
        this.returnToLogin()
        }
      }).catch(err => {
        this.emailResetError = true
        this.emailResetErrorText = err.message
      })
    },

    returnToLogin() {
      this.clearEmailSendFields()
      this.currform = "login";
    },

    clearEmailSendFields() {
      this.emailResetError = false
      this.emailReset = ""; 
      this.emailResetErrorText = "";
    },

    returnToEmail() {
      this.clearResetPasswordFields()
      this.currform = "resetpassemail";
    },

    loginNotify(e) {
      this.login({
        email: this.email,
        password: this.password
      }).then(response => {
        if (response) {
          if (this.rememberme) {
            localStorage.setItem('rememberme',true)
            localStorage.setItem('email', this.email)
          }
          this.email = ""
          this.password = ""
          this.loginError = false;
          this.$router.push({name: "cases"})
        }
      }).catch(err => {
          this.loginError = true;
          this.loginErrorText = err.message
      })
    }
  },
  mounted() {
    particlesJS("particles-js", {
      particles: {
        number: {
          value: 80,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: "#ffffff"
        },
        shape: {
          type: "circle",
          stroke: {
            width: 0,
            color: "#000000"
          },
          polygon: {
            nb_sides: 5
          },
          image: {
            src: "img/github.svg",
            width: 100,
            height: 100
          }
        },
        opacity: {
          value: 0.5,
          random: false,
          anim: {
            enable: false,
            speed: 1,
            opacity_min: 0.1,
            sync: false
          }
        },
        size: {
          value: 3,
          random: true,
          anim: {
            enable: false,
            speed: 40,
            size_min: 0.1,
            sync: false
          }
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: "#ffffff",
          opacity: 0.4,
          width: 1
        },
        move: {
          enable: true,
          speed: 6,
          direction: "none",
          random: false,
          straight: false,
          out_mode: "out",
          bounce: false,
          attract: {
            enable: false,
            rotateX: 600,
            rotateY: 1200
          }
        }
      },
      interactivity: {
        detect_on: "canvas",
        events: {
          onhover: {
            enable: true,
            mode: "grab"
          },
          onclick: {
            enable: true,
            mode: "push"
          },
          resize: true
        },
        modes: {
          grab: {
            distance: 140,
            line_linked: {
              opacity: 1
            }
          },
          bubble: {
            distance: 400,
            size: 40,
            duration: 2,
            opacity: 8,
            speed: 3
          },
          repulse: {
            distance: 200,
            duration: 0.4
          },
          push: {
            particles_nb: 4
          },
          remove: {
            particles_nb: 2
          }
        }
      },
      retina_detect: true
    });
  }
};
</script>

<style>
#particles-js {
  position: absolute;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 50%;
}
.normal_gradient {
  background: linear-gradient(145deg, rgb(74, 94, 137) 15%, #b61924 70%);
}
.dark_gradient {
  background: linear-gradient(145deg, rgb(11, 26, 61) 15%, #4c1014 70%);
}
.login-form {
  position: absolute;
}
.resetpasswordtext {
  text-decoration: underline;
  color: blue;
  cursor: pointer;
}
</style>
