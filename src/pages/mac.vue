<template>
  <CustomTable
    :data="maclist"
    :columns="columns"
    @reset="reset"
    :tabletitle="$t('macAddressesBlackList')"
    :isDeleteAvailable="true"
    :isAddAvailable="true"
    :is_add_disabled="!validateMacAddress(mac.mac)"
    :is_edit_disabled="!validateMacAddress(mac.mac)"
    :isEditAvailable="true"
    @addRow="addMacBlackListInner"
    @deleteRow="deleteMacBlackListInner"
    @saveEdit="saveEdit"
    @onEditClick="onEditClick"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    :loading="loading"
              @onRefresh="onRequest({filter, pagination})"
                 :filter.sync="filter"

  >
     <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeMacAddress')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('macAddress')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="mac.mac"
                  outlined
                  label="Пример: fa:09:91:d5:e4:5a"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="mac.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('addMacAddress')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('macAddress')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="mac.mac"
                  outlined
                  label="Пример: fa:09:91:d5:e4:5a"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="mac.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      mac: {
        mac: "",
        note: ""
      },
      loading: false,
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      filter: ""
    };
  },

  computed: {
    ...mapState({
      maclist: state => state.admin.maclist
    }),
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "mac",
          label: "MAC",
          field: row => row.mac,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getMacBlackList: "admin/getMacBlackList",
      addMacBlackList: "admin/addMacBlackList",
      deleteMacBlackList: "admin/deleteMacBlackList",
      editMacBlackList: "admin/editMacBlackList"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber  = await this.getMacBlackList({
          page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "id,mac"
        ),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false
    },

    onEditClick(row) {
      this.mac = { ...row };
    },

    saveEdit() {
      this.editMacBlackList(this.mac)
        .then(response => {
          this.$q.notify({
            message: `MAC ${response.id} изменен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    validateMacAddress(mac) {
      let regexp = /^(([A-Fa-f0-9]{2}[:]){5}[A-Fa-f0-9]{2}[,]?)+$/i;
      if (regexp.test(mac)) {
        return true;
      }
      return false;
    },

    reset() {
      this.mac = {
        mac: "",
        note: ""
      };
    },

    deleteMacBlackListInner(row) {
      this.deleteMacBlackList(row.id)
        .then(response => {
          this.$q.notify({
            message: `MAC ${row.id} удален из черного списка`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addMacBlackListInner() {
      this.addMacBlackList(this.mac)
        .then(response => {
          this.$q.notify({
            message: `MAC ${this.mac.mac} добавлен в черный список`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
